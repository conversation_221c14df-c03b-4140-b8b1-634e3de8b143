# نظام إدارة الشركة الشامل

نظام احترافي شامل لإدارة جميع عمليات الشركة من مبيعات ومشتريات ومخزون وفواتير.

## المميزات الرئيسية

### 📊 لوحة التحكم
- نظرة عامة شاملة على أداء الشركة
- إحصائيات المبيعات والمشتريات
- تنبيهات المخزون
- الرسوم البيانية التفاعلية

### 🏢 إدارة الشركة
- بيانات الشركة الأساسية
- الرقم الضريبي والسجل التجاري
- معلومات الاتصال والعنوان
- رفع وإدارة شعار الشركة

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء
- معلومات الموردين وتفاصيل الاتصال
- أرقام الفواتير والسجلات الضريبية
- تاريخ التعاملات

### 📦 إدارة المنتجات والمخزون
- كتالوج شامل للمنتجات
- تتبع مستويات المخزون
- تنبيهات نفاذ المخزون
- إدارة الأسعار والتكاليف

### 🧾 نظام الفواتير
- إنشاء فواتير مبيعات ومشتريات
- طباعة الفواتير بتصميم احترافي
- ترقيم تلقائي للفواتير
- حفظ وأرشفة الفواتير

### 📈 التقارير والإحصائيات
- تقارير المبيعات والمشتريات
- تقارير المخزون
- الرسوم البيانية التفاعلية
- تصدير التقارير

## التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Charts**: Recharts
- **Forms**: React Hook Form مع Zod validation

## التثبيت والتشغيل

```bash
# تثبيت المكتبات
npm install

# تشغيل المشروع في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل المشروع في وضع الإنتاج
npm start
```

## هيكل المشروع

```
src/
├── app/                    # صفحات Next.js
│   ├── globals.css        # الأنماط العامة
│   ├── layout.tsx         # التخطيط الرئيسي
│   └── page.tsx           # الصفحة الرئيسية
├── components/            # المكونات
│   ├── layout/           # مكونات التخطيط
│   │   ├── Header.tsx    # رأس الصفحة
│   │   └── Sidebar.tsx   # الشريط الجانبي
│   ├── dashboard/        # مكونات لوحة التحكم
│   ├── company/          # مكونات إدارة الشركة
│   ├── customers/        # مكونات إدارة العملاء
│   ├── suppliers/        # مكونات إدارة الموردين
│   ├── products/         # مكونات إدارة المنتجات
│   ├── inventory/        # مكونات إدارة المخزون
│   ├── sales/            # مكونات إدارة المبيعات
│   ├── purchases/        # مكونات إدارة المشتريات
│   ├── invoices/         # مكونات إدارة الفواتير
│   └── reports/          # مكونات التقارير
```

## الميزات المتقدمة

- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **واجهة عربية**: دعم كامل للغة العربية
- **تصميم احترافي**: واجهة مستخدم حديثة وجذابة
- **أداء عالي**: تحسينات الأداء مع Next.js
- **قابلية التوسع**: بنية قابلة للتطوير والتوسع

## المطور

تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والموثوقية.

## الدعم

للحصول على الدعم أو الاستفسارات، يرجى التواصل معنا.
