'use client'

import { AlertTriangle, Package, TrendingDown } from 'lucide-react'

const lowStockItems = [
  {
    id: 1,
    name: 'لابتوب ديل XPS 13',
    currentStock: 2,
    minStock: 5,
    category: 'إلكترونيات'
  },
  {
    id: 2,
    name: 'ماوس لوجيتك MX Master',
    currentStock: 1,
    minStock: 10,
    category: 'ملحقات'
  },
  {
    id: 3,
    name: 'كيبورد ميكانيكي',
    currentStock: 3,
    minStock: 8,
    category: 'ملحقات'
  },
  {
    id: 4,
    name: 'شاشة سامسونج 27 بوصة',
    currentStock: 0,
    minStock: 3,
    category: 'شاشات'
  }
]

export default function InventoryAlerts() {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2 space-x-reverse">
          <AlertTriangle className="w-5 h-5 text-orange-500" />
          <h3 className="text-lg font-semibold text-gray-900">تنبيهات المخزون</h3>
        </div>
        <span className="text-sm text-gray-500">{lowStockItems.length} تنبيه</span>
      </div>

      <div className="space-y-4">
        {lowStockItems.map((item) => (
          <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                item.currentStock === 0 
                  ? 'bg-red-100' 
                  : 'bg-orange-100'
              }`}>
                <Package className={`w-5 h-5 ${
                  item.currentStock === 0 
                    ? 'text-red-600' 
                    : 'text-orange-600'
                }`} />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 text-sm">{item.name}</h4>
                <p className="text-xs text-gray-500">{item.category}</p>
              </div>
            </div>
            
            <div className="text-left">
              <div className={`text-sm font-semibold ${
                item.currentStock === 0 
                  ? 'text-red-600' 
                  : 'text-orange-600'
              }`}>
                {item.currentStock === 0 ? 'نفد المخزون' : `${item.currentStock} متبقي`}
              </div>
              <div className="text-xs text-gray-500">
                الحد الأدنى: {item.minStock}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <button className="w-full flex items-center justify-center space-x-2 space-x-reverse py-2 px-4 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors">
          <TrendingDown className="w-4 h-4" />
          <span className="text-sm font-medium">عرض جميع التنبيهات</span>
        </button>
      </div>
    </div>
  )
}
