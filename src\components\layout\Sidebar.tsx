'use client'

import { 
  LayoutDashboard, 
  Building2, 
  Users, 
  Truck, 
  Package, 
  Warehouse, 
  ShoppingCart, 
  ShoppingBag, 
  FileText, 
  BarChart3,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface SidebarProps {
  activeTab: string
  setActiveTab: (tab: string) => void
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

const menuItems = [
  { id: 'dashboard', label: 'لوحة التحكم', icon: LayoutDashboard },
  { id: 'company', label: 'بيانات الشركة', icon: Building2 },
  { id: 'customers', label: 'العملاء', icon: Users },
  { id: 'suppliers', label: 'الموردين', icon: Truck },
  { id: 'products', label: 'المنتجات', icon: Package },
  { id: 'inventory', label: 'المخزون', icon: Warehouse },
  { id: 'sales', label: 'المبيعات', icon: ShoppingCart },
  { id: 'purchases', label: 'المشتريات', icon: ShoppingBag },
  { id: 'invoices', label: 'الفواتير', icon: FileText },
  { id: 'reports', label: 'التقارير', icon: BarChart3 },
]

export default function Sidebar({ activeTab, setActiveTab, isOpen, setIsOpen }: SidebarProps) {
  return (
    <div className={`fixed right-0 top-0 h-full bg-white shadow-xl transition-all duration-300 z-50 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {isOpen && (
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">إدارة الشركة</h1>
              <p className="text-xs text-gray-500">نظام شامل</p>
            </div>
          </div>
        )}
        
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
        >
          {isOpen ? (
            <ChevronRight className="w-5 h-5 text-gray-600" />
          ) : (
            <ChevronLeft className="w-5 h-5 text-gray-600" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.id
          
          return (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`w-full flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 ${
                isActive
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
              title={!isOpen ? item.label : undefined}
            >
              <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-600'}`} />
              {isOpen && (
                <span className="font-medium">{item.label}</span>
              )}
            </button>
          )
        })}
      </nav>

      {/* Footer */}
      {isOpen && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-4 text-white">
            <h3 className="font-semibold text-sm">نظام احترافي</h3>
            <p className="text-xs opacity-90 mt-1">
              إدارة شاملة لجميع عمليات الشركة
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
