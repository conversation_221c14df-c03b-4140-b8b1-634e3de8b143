'use client'

import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Package, 
  Users,
  AlertTriangle,
  Eye
} from 'lucide-react'
import StatsCard from './StatsCard'
import RecentTransactions from './RecentTransactions'
import InventoryAlerts from './InventoryAlerts'
import SalesChart from './SalesChart'

export default function Dashboard() {
  const stats = [
    {
      title: 'إجمالي المبيعات',
      value: '125,430',
      unit: 'ر.س',
      change: '+12.5%',
      changeType: 'increase' as const,
      icon: DollarSign,
      color: 'green'
    },
    {
      title: 'عدد الفواتير',
      value: '1,234',
      unit: 'فاتورة',
      change: '+8.2%',
      changeType: 'increase' as const,
      icon: ShoppingCart,
      color: 'blue'
    },
    {
      title: 'المنتجات',
      value: '456',
      unit: 'منتج',
      change: '+3.1%',
      changeType: 'increase' as const,
      icon: Package,
      color: 'purple'
    },
    {
      title: 'العملاء',
      value: '89',
      unit: 'عميل',
      change: '+15.3%',
      changeType: 'increase' as const,
      icon: Users,
      color: 'orange'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-1">نظرة عامة على أداء الشركة</p>
        </div>
        <div className="flex items-center space-x-3 space-x-reverse">
          <button className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
            تصدير التقرير
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            تحديث البيانات
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts and Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sales Chart */}
        <div className="lg:col-span-2">
          <SalesChart />
        </div>

        {/* Inventory Alerts */}
        <div>
          <InventoryAlerts />
        </div>
      </div>

      {/* Recent Transactions */}
      <div>
        <RecentTransactions />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <ShoppingCart className="w-8 h-8 text-primary-600 mb-2" />
            <span className="text-sm font-medium text-gray-700">فاتورة جديدة</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Package className="w-8 h-8 text-green-600 mb-2" />
            <span className="text-sm font-medium text-gray-700">إضافة منتج</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Users className="w-8 h-8 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-gray-700">عميل جديد</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Eye className="w-8 h-8 text-purple-600 mb-2" />
            <span className="text-sm font-medium text-gray-700">عرض التقارير</span>
          </button>
        </div>
      </div>
    </div>
  )
}
