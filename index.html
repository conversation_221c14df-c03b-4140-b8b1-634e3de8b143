<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الشركة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body { font-family: 'Cairo', system-ui, sans-serif; }
        .fade-in { animation: fadeIn 0.3s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed right-0 top-0 h-full bg-white shadow-xl transition-all duration-300 z-50 w-64">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900">إدارة الشركة</h1>
                        <p class="text-xs text-gray-500">نظام شامل</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="p-4 space-y-2">
                <button onclick="showPage('dashboard')" class="nav-btn w-full flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 bg-primary-600 text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span class="font-medium">لوحة التحكم</span>
                </button>
                
                <button onclick="showPage('company')" class="nav-btn w-full flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 text-gray-700 hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span class="font-medium">بيانات الشركة</span>
                </button>
                
                <button onclick="showPage('customers')" class="nav-btn w-full flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 text-gray-700 hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span class="font-medium">العملاء</span>
                </button>
                
                <button onclick="showPage('products')" class="nav-btn w-full flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 text-gray-700 hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <span class="font-medium">المنتجات</span>
                </button>
                
                <button onclick="showPage('invoices')" class="nav-btn w-full flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 text-gray-700 hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="font-medium">الفواتير</span>
                </button>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col mr-64">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <h2 id="page-title" class="text-2xl font-bold text-gray-900">لوحة التحكم</h2>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="text-sm text-gray-600">
                            <span id="current-date"></span>
                        </div>
                        <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-auto p-6">
                <div id="content" class="max-w-7xl mx-auto">
                    <!-- Dashboard Content -->
                    <div id="dashboard-page" class="page-content">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <!-- Stats Cards -->
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 mb-1">إجمالي المبيعات</p>
                                        <h3 class="text-2xl font-bold text-gray-900">125,430 ر.س</h3>
                                        <p class="text-sm text-green-600 mt-1">+12.5% من الشهر الماضي</p>
                                    </div>
                                    <div class="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 mb-1">عدد الفواتير</p>
                                        <h3 class="text-2xl font-bold text-gray-900">1,234</h3>
                                        <p class="text-sm text-blue-600 mt-1">+8.2% من الشهر الماضي</p>
                                    </div>
                                    <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 mb-1">المنتجات</p>
                                        <h3 class="text-2xl font-bold text-gray-900">456</h3>
                                        <p class="text-sm text-purple-600 mt-1">+3.1% من الشهر الماضي</p>
                                    </div>
                                    <div class="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 mb-1">العملاء</p>
                                        <h3 class="text-2xl font-bold text-gray-900">89</h3>
                                        <p class="text-sm text-orange-600 mt-1">+15.3% من الشهر الماضي</p>
                                    </div>
                                    <div class="w-12 h-12 bg-orange-50 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Transactions -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">المعاملات الأخيرة</h3>
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-600">رقم الفاتورة</th>
                                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-600">العميل</th>
                                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-600">المبلغ</th>
                                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-600">التاريخ</th>
                                            <th class="text-right py-3 px-4 text-sm font-medium text-gray-600">الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-4 px-4 font-medium text-gray-900">INV-001</td>
                                            <td class="py-4 px-4 text-gray-900">أحمد محمد علي</td>
                                            <td class="py-4 px-4 font-semibold text-gray-900">15,750 ر.س</td>
                                            <td class="py-4 px-4 text-gray-600">2024-01-15</td>
                                            <td class="py-4 px-4">
                                                <span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">مكتملة</span>
                                            </td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-4 px-4 font-medium text-gray-900">INV-002</td>
                                            <td class="py-4 px-4 text-gray-900">فاطمة أحمد</td>
                                            <td class="py-4 px-4 font-semibold text-gray-900">3,200 ر.س</td>
                                            <td class="py-4 px-4 text-gray-600">2024-01-14</td>
                                            <td class="py-4 px-4">
                                                <span class="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">مكتملة</span>
                                            </td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-4 px-4 font-medium text-gray-900">INV-003</td>
                                            <td class="py-4 px-4 text-gray-900">محمد عبدالله</td>
                                            <td class="py-4 px-4 font-semibold text-gray-900">12,400 ر.س</td>
                                            <td class="py-4 px-4 text-gray-600">2024-01-13</td>
                                            <td class="py-4 px-4">
                                                <span class="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">معلقة</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Other Pages (Hidden by default) -->
                    <div id="company-page" class="page-content hidden">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">بيانات الشركة</h3>
                            <p class="text-gray-600">صفحة إدارة بيانات الشركة قيد التطوير...</p>
                        </div>
                    </div>

                    <div id="customers-page" class="page-content hidden">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">إدارة العملاء</h3>
                            <p class="text-gray-600">صفحة إدارة العملاء قيد التطوير...</p>
                        </div>
                    </div>

                    <div id="products-page" class="page-content hidden">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">إدارة المنتجات</h3>
                            <p class="text-gray-600">صفحة إدارة المنتجات قيد التطوير...</p>
                        </div>
                    </div>

                    <div id="invoices-page" class="page-content hidden">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">إدارة الفواتير</h3>
                            <p class="text-gray-600">صفحة إدارة الفواتير قيد التطوير...</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Update current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.add('hidden');
            });
            
            // Show selected page
            document.getElementById(pageId + '-page').classList.remove('hidden');
            
            // Update page title
            const titles = {
                'dashboard': 'لوحة التحكم',
                'company': 'بيانات الشركة',
                'customers': 'إدارة العملاء',
                'products': 'إدارة المنتجات',
                'invoices': 'إدارة الفواتير'
            };
            document.getElementById('page-title').textContent = titles[pageId];
            
            // Update active nav button
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('bg-primary-600', 'text-white');
                btn.classList.add('text-gray-700', 'hover:bg-gray-100');
            });
            event.target.classList.add('bg-primary-600', 'text-white');
            event.target.classList.remove('text-gray-700', 'hover:bg-gray-100');
        }
    </script>
</body>
</html>
