'use client'

import { ArrowUpR<PERSON>, ArrowDownLeft, Eye, MoreHorizontal } from 'lucide-react'

const transactions = [
  {
    id: 'INV-001',
    type: 'sale',
    customer: 'أحمد محمد علي',
    amount: 15750,
    date: '2024-01-15',
    status: 'completed'
  },
  {
    id: 'PUR-002',
    type: 'purchase',
    supplier: 'شركة التقنية المتقدمة',
    amount: 8500,
    date: '2024-01-14',
    status: 'pending'
  },
  {
    id: 'INV-003',
    type: 'sale',
    customer: 'فاطمة أحمد',
    amount: 3200,
    date: '2024-01-14',
    status: 'completed'
  },
  {
    id: 'INV-004',
    type: 'sale',
    customer: 'محمد عبدالله',
    amount: 12400,
    date: '2024-01-13',
    status: 'completed'
  },
  {
    id: 'PUR-005',
    type: 'purchase',
    supplier: 'مؤسسة الإلكترونيات',
    amount: 25600,
    date: '2024-01-13',
    status: 'completed'
  }
]

export default function RecentTransactions() {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">المعاملات الأخيرة</h3>
        <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
          عرض الكل
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">رقم المعاملة</th>
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">النوع</th>
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">العميل/المورد</th>
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">المبلغ</th>
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">التاريخ</th>
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">الحالة</th>
              <th className="text-right py-3 px-4 text-sm font-medium text-gray-600">إجراءات</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map((transaction) => (
              <tr key={transaction.id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-4 px-4">
                  <span className="font-medium text-gray-900">{transaction.id}</span>
                </td>
                <td className="py-4 px-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {transaction.type === 'sale' ? (
                      <>
                        <ArrowUpRight className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-green-700 bg-green-50 px-2 py-1 rounded-full">
                          مبيعات
                        </span>
                      </>
                    ) : (
                      <>
                        <ArrowDownLeft className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-blue-700 bg-blue-50 px-2 py-1 rounded-full">
                          مشتريات
                        </span>
                      </>
                    )}
                  </div>
                </td>
                <td className="py-4 px-4">
                  <span className="text-gray-900">
                    {transaction.type === 'sale' ? transaction.customer : transaction.supplier}
                  </span>
                </td>
                <td className="py-4 px-4">
                  <span className="font-semibold text-gray-900">
                    {transaction.amount.toLocaleString()} ر.س
                  </span>
                </td>
                <td className="py-4 px-4">
                  <span className="text-gray-600">
                    {new Date(transaction.date).toLocaleDateString('ar-EG')}
                  </span>
                </td>
                <td className="py-4 px-4">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    transaction.status === 'completed'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {transaction.status === 'completed' ? 'مكتملة' : 'معلقة'}
                  </span>
                </td>
                <td className="py-4 px-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Eye className="w-4 h-4 text-gray-600" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <MoreHorizontal className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
