'use client'

import { useState } from 'react'
import Sidebar from '@/components/layout/Sidebar'
import Header from '@/components/layout/Header'
import Dashboard from '@/components/dashboard/Dashboard'
import CompanySettings from '@/components/company/CompanySettings'
import CustomersPage from '@/components/customers/CustomersPage'
import SuppliersPage from '@/components/suppliers/SuppliersPage'
import ProductsPage from '@/components/products/ProductsPage'
import InventoryPage from '@/components/inventory/InventoryPage'
import SalesPage from '@/components/sales/SalesPage'
import PurchasesPage from '@/components/purchases/PurchasesPage'
import InvoicesPage from '@/components/invoices/InvoicesPage'
import ReportsPage from '@/components/reports/ReportsPage'

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(true)

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />
      case 'company':
        return <CompanySettings />
      case 'customers':
        return <CustomersPage />
      case 'suppliers':
        return <SuppliersPage />
      case 'products':
        return <ProductsPage />
      case 'inventory':
        return <InventoryPage />
      case 'sales':
        return <SalesPage />
      case 'purchases':
        return <PurchasesPage />
      case 'invoices':
        return <InvoicesPage />
      case 'reports':
        return <ReportsPage />
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar 
        activeTab={activeTab} 
        setActiveTab={setActiveTab}
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
      />
      
      <div className={`flex-1 flex flex-col transition-all duration-300 ${
        sidebarOpen ? 'mr-64' : 'mr-16'
      }`}>
        <Header 
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
        />
        
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  )
}
