'use client'

import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react'

interface StatsCardProps {
  title: string
  value: string
  unit: string
  change: string
  changeType: 'increase' | 'decrease'
  icon: LucideIcon
  color: 'green' | 'blue' | 'purple' | 'orange' | 'red'
}

const colorClasses = {
  green: {
    bg: 'bg-green-50',
    icon: 'text-green-600',
    change: 'text-green-600'
  },
  blue: {
    bg: 'bg-blue-50',
    icon: 'text-blue-600',
    change: 'text-blue-600'
  },
  purple: {
    bg: 'bg-purple-50',
    icon: 'text-purple-600',
    change: 'text-purple-600'
  },
  orange: {
    bg: 'bg-orange-50',
    icon: 'text-orange-600',
    change: 'text-orange-600'
  },
  red: {
    bg: 'bg-red-50',
    icon: 'text-red-600',
    change: 'text-red-600'
  }
}

export default function StatsCard({
  title,
  value,
  unit,
  change,
  changeType,
  icon: Icon,
  color
}: StatsCardProps) {
  const colors = colorClasses[color]
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <div className="flex items-baseline space-x-2 space-x-reverse">
            <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
            <span className="text-sm text-gray-500">{unit}</span>
          </div>
          <div className="flex items-center mt-2">
            {changeType === 'increase' ? (
              <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
            ) : (
              <TrendingDown className="w-4 h-4 text-red-500 ml-1" />
            )}
            <span className={`text-sm font-medium ${
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}>
              {change}
            </span>
            <span className="text-sm text-gray-500 mr-1">من الشهر الماضي</span>
          </div>
        </div>
        
        <div className={`w-12 h-12 ${colors.bg} rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 ${colors.icon}`} />
        </div>
      </div>
    </div>
  )
}
