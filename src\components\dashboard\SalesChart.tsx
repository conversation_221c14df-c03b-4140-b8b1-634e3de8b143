'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

const data = [
  { name: 'يناير', sales: 45000, purchases: 32000 },
  { name: 'فبراير', sales: 52000, purchases: 38000 },
  { name: 'مارس', sales: 48000, purchases: 35000 },
  { name: 'أبريل', sales: 61000, purchases: 42000 },
  { name: 'مايو', sales: 55000, purchases: 40000 },
  { name: 'يونيو', sales: 67000, purchases: 45000 },
]

export default function SalesChart() {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">المبيعات والمشتريات</h3>
          <p className="text-sm text-gray-600">آخر 6 أشهر</p>
        </div>
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="flex items-center space-x-2 space-x-reverse">
            <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
            <span className="text-sm text-gray-600">المبيعات</span>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">المشتريات</span>
          </div>
        </div>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
              tickFormatter={(value) => `${value / 1000}ك`}
            />
            <Tooltip 
              formatter={(value: number, name: string) => [
                `${value.toLocaleString()} ر.س`,
                name === 'sales' ? 'المبيعات' : 'المشتريات'
              ]}
              labelStyle={{ color: '#374151' }}
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Bar 
              dataKey="sales" 
              fill="#2563eb" 
              radius={[4, 4, 0, 0]}
              name="sales"
            />
            <Bar 
              dataKey="purchases" 
              fill="#10b981" 
              radius={[4, 4, 0, 0]}
              name="purchases"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
